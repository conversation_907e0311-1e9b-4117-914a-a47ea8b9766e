name: deploy

on:
  push:
  workflow_dispatch:
    inputs:
      confirm_publish:
        description: 'Type YES to confirm publishing to PyPI'
        required: true
        type: string

jobs:
  build-n-publish:
    runs-on: ubuntu-latest
    if: |
      github.event_name == 'push' && startsWith(github.event.ref, 'refs/tags') ||
      (github.event_name == 'workflow_dispatch' && inputs.confirm_publish == 'YES')
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python 3.10
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      - name: Build lagent
        run: |
          pip install wheel
          python setup.py sdist bdist_wheel
      - name: Publish distribution to PyPI
        run: |
          pip install twine
          twine upload dist/* -u __token__ -p ${{ secrets.pypi_password }}
