name: lint

on: [push, pull_request]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python 3.10
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      - name: Install pre-commit hook
        run: |
          pip install pre-commit==3.8.0 mmengine==0.10.5
          pre-commit install
      - name: Linting
        run: pre-commit run --all-files
