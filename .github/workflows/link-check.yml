name: 'Link check'

on:
  schedule:
    # check links at 01:30 a.m. every day
    - cron: '30 1 * * *'

  workflow_dispatch: # allow manual trigger

jobs:
  link-check:
    runs-on: ubuntu-latest
    steps:
      # - uses: actions/checkout@v3

      - name: Install linkchecker
        run: |
          pip install linkchecker

      - name: Run linkchecker
        run: |
          linkchecker https://opencompass.readthedocs.io/ --no-robots -t 30 --no-warnings \
            --ignore-url "https://opencompass.readthedocs.io/.*/static/images/opencompass_logo.svg" \
            --ignore-url "https://opencompass.readthedocs.io/.*/_static/images/icon-menu-dots.svg" \
            --ignore-url "https://opencompass.readthedocs.io/policy" \
            --ignore-url "https://opencompass.readthedocs.io/(en|zh_CN)/[0-9a-f]{40}/.*"
