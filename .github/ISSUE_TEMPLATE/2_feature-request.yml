name: 🚀 Feature request
description: Suggest an idea for this project
labels: ["enhancement"]
title: "[Feature] "
body:
  - type: markdown
    attributes:
      value: |
        For general questions or idea discussions, please post it to our [**Forum**](https://github.com/open-compass/opencompass/discussions).
        If you have already implemented the feature, we strongly appreciate you creating a new PR according to [the tutorial](https://opencompass.readthedocs.io/en/master/community/CONTRIBUTING.html)!

  - type: textarea
    id: describe
    validations:
      required: true
    attributes:
      label: Describe the feature
      description: |
        What kind of feature do you want OpenCompass to add. If there is an official code release or third-party implementation, please also provide the information here, which would be very helpful.
      placeholder: |
        A clear and concise description of the motivation of the feature.
        Ex1. It is inconvenient when \[....\].
        Ex2. There is a recent paper \[....\], which is very helpful for \[....\].

  - type: checkboxes
    id: pr
    attributes:
      label: Will you implement it?
      options:
        - label: I would like to implement this feature and create a PR!
