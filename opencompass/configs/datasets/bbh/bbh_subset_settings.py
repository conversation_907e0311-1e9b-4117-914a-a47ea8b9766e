settings = [
    ('temporal_sequences', 'mcq'),
    ('disambiguation_qa', 'mcq'),
    ('date_understanding', 'mcq'),
    ('tracking_shuffled_objects_three_objects', 'mcq'),
    ('penguins_in_a_table', 'mcq'),
    ('geometric_shapes', 'mcq'),
    ('snarks', 'mcq'),
    ('ruin_names', 'mcq'),
    ('tracking_shuffled_objects_seven_objects', 'mcq'),
    ('tracking_shuffled_objects_five_objects', 'mcq'),
    ('logical_deduction_three_objects', 'mcq'),
    ('hyperbaton', 'mcq'),
    ('logical_deduction_five_objects', 'mcq'),
    ('logical_deduction_seven_objects', 'mcq'),
    ('movie_recommendation', 'mcq'),
    ('salient_translation_error_detection', 'mcq'),
    ('reasoning_about_colored_objects', 'mcq'),
    ('multistep_arithmetic_two', 'free_form'),
    ('navigate', 'free_form'),
    ('dyck_languages', 'free_form'),
    ('word_sorting', 'free_form'),
    ('sports_understanding', 'free_form'),
    ('boolean_expressions', 'free_form'),
    ('object_counting', 'free_form'),
    ('formal_fallacies', 'free_form'),
    ('causal_judgement', 'free_form'),
    ('web_of_lies', 'free_form'),
]
