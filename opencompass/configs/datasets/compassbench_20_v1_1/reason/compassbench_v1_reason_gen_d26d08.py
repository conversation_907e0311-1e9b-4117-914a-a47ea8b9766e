compassbench_v1_reason_datasets = [
    dict(path='data/compassbench_v1.1/reason/CN_Commonsense.jsonl',
         abbr='reasonbench_cn_commonsense_circular', data_type='circular-mcq', infer_method='gen'),
    dict(path='data/compassbench_v1.1/reason/CN_Abductive_Alphanlg_translated.jsonl',
         abbr='reasonbench_cn_abductive_alphanlg_translated_circular', data_type='circular-mcq', infer_method='gen'),
    dict(path='data/compassbench_v1.1/reason/CN_Deductive_BBH3obj_translated.jsonl',
         abbr='reasonbench_cn_deductive_bbh3obj_translated_circular', data_type='circular-mcq', infer_method='gen'),
    dict(path='data/compassbench_v1.1/reason/CN_Deductive_LogiQA_zh.jsonl',
         abbr='reasonbench_cn_deductive_logiqa_zh_circular', data_type='circular-mcq', infer_method='gen'),
    dict(path='data/compassbench_v1.1/reason/CN_Inductive_DEER_translated.jsonl',
         abbr='reasonbench_cn_inductive_deer_translated_circular', data_type='circular-mcq', infer_method='gen'),
    dict(path='data/compassbench_v1.1/reason/CN_Inductive_selfgenerated.jsonl',
         abbr='reasonbench_cn_inductive_selfgenerated_circular', data_type='circular-mcq', infer_method='gen'),
    dict(path='data/compassbench_v1.1/reason/EN_Commonsense.jsonl',
         abbr='reasonbench_en_commonsense_circular', data_type='circular-mcq', infer_method='gen'),
    dict(path='data/compassbench_v1.1/reason/EN_Abductive_Alphanlg.jsonl',
         abbr='reasonbench_en_abductive_alphanlg_circular', data_type='circular-mcq', infer_method='gen'),
    dict(path='data/compassbench_v1.1/reason/EN_Deductive_BBH7obj.jsonl',
         abbr='reasonbench_en_deductive_bbh7obj_circular', data_type='circular-mcq', infer_method='gen'),
    dict(path='data/compassbench_v1.1/reason/EN_Deductive_LogiQA_zh_translated.jsonl',
         abbr='reasonbench_en_deductive_logiqa_zh_translated_circular', data_type='circular-mcq', infer_method='gen'),
    dict(path='data/compassbench_v1.1/reason/EN_Deductive_OCNLI_translated.jsonl',
         abbr='reasonbench_en_deductive_ocnli_translated_circular', data_type='circular-mcq', infer_method='gen'),
    dict(path='data/compassbench_v1.1/reason/EN_Inductive_DEER.jsonl',
         abbr='reasonbench_en_inductive_deer_circular', data_type='circular-mcq', infer_method='gen'),
    dict(path='data/compassbench_v1.1/reason/EN_Inductive_selfgenerated.jsonl',
         abbr='reasonbench_en_inductive_selfgenerated_circular', data_type='circular-mcq', infer_method='gen'),
]
