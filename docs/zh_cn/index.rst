欢迎来到 OpenCompass 中文教程！
==========================================

OpenCompass 上手路线
-------------------------------

为了用户能够快速上手，我们推荐以下流程：

- 对于想要使用 OpenCompass 的用户，我们推荐先阅读 开始你的第一步_ 部分来设置环境，并启动一个迷你实验熟悉流程。

- 对于一些基础使用，我们建议用户阅读 教程_ 。

- 如果您想调整提示词（prompt），您可以浏览 提示词_ 。

- 若您想进行更多模块的自定义，例如增加数据集和模型，我们提供了 进阶教程_ 。

- 还有更多实用的工具，如提示词预览、飞书机器人上报等功能，我们同样提供了 工具_ 教程。


我们始终非常欢迎用户的 PRs 和 Issues 来完善 OpenCompass！

.. _开始你的第一步:
.. toctree::
   :maxdepth: 1
   :caption: 开始你的第一步

   get_started/installation.md
   get_started/quick_start.md
   get_started/faq.md

.. _教程:
.. toctree::
   :maxdepth: 1
   :caption: 教程

   user_guides/framework_overview.md
   user_guides/config.md
   user_guides/datasets.md
   user_guides/models.md
   user_guides/evaluation.md
   user_guides/experimentation.md
   user_guides/metrics.md
   user_guides/deepseek_r1.md
   user_guides/interns1.md

.. _提示词:
.. toctree::
   :maxdepth: 1
   :caption: 提示词

   prompt/overview.md
   prompt/prompt_template.md
   prompt/meta_template.md
   prompt/chain_of_thought.md

.. _进阶教程:
.. toctree::
   :maxdepth: 1
   :caption: 进阶教程

   advanced_guides/new_dataset.md
   advanced_guides/custom_dataset.md
   advanced_guides/new_model.md
   advanced_guides/evaluation_lmdeploy.md
   advanced_guides/accelerator_intro.md
   advanced_guides/math_verify.md
   advanced_guides/llm_judge.md
   advanced_guides/code_eval.md
   advanced_guides/code_eval_service.md
   advanced_guides/subjective_evaluation.md
   advanced_guides/persistence.md

.. _工具:
.. toctree::
   :maxdepth: 1
   :caption: 工具

   tools.md

.. _数据集列表:
.. toctree::
   :maxdepth: 1
   :caption: 数据集列表

   dataset_statistics.md

.. _其他说明:
.. toctree::
   :maxdepth: 1
   :caption: 其他说明

   notes/contribution_guide.md
   notes/academic.md

索引与表格
==================

* :ref:`genindex`
* :ref:`search`
