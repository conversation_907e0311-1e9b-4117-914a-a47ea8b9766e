# 整体概括

## 评测对象

本算法库的主要评测对象为语言大模型与多模态大模型。我们以语言大模型为例介绍评测的具体模型类型。

- 基座模型：一般是经过海量的文本数据以自监督学习的方式进行训练获得的模型（如OpenAI的GPT-3，Meta的LLaMA），往往具有强大的文字续写能力。

- 对话模型：一般是在的基座模型的基础上，经过指令微调或人类偏好对齐获得的模型（如OpenAI的ChatGPT、上海人工智能实验室的书生·浦语），能理解人类指令，具有较强的对话能力。

## 工具架构

![framework-cn](https://github.com/tonysy/opencompass/assets/7881589/374d9cec-2ebc-442a-ab11-191a7d5cf5e9)

- 模型层：大模型评测所涉及的主要模型种类，OpenCompass以基座模型和对话模型作为重点评测对象。
- 能力层：OpenCompass从本方案从通用能力和特色能力两个方面来进行评测维度设计。在模型通用能力方面，从语言、知识、理解、推理、安全等多个能力维度进行评测。在特色能力方面，从长文本、代码、工具、知识增强等维度进行评测。
- 方法层：OpenCompass采用客观评测与主观评测两种评测方式。客观评测能便捷地评估模型在具有确定答案（如选择，填空，封闭式问答等）的任务上的能力，主观评测能评估用户对模型回复的真实满意度，OpenCompass采用基于模型辅助的主观评测和基于人类反馈的主观评测两种方式。
- 工具层：OpenCompass提供丰富的功能支持自动化地开展大语言模型的高效评测。包括分布式评测技术，提示词工程，对接评测数据库，评测榜单发布，评测报告生成等诸多功能。

## 能力维度

### 设计思路

为准确、全面、系统化地评估大语言模型的能力，OpenCompass从通用人工智能的角度出发，结合学术界的前沿进展和工业界的最佳实践，提出一套面向实际应用的模型能力评价体系。OpenCompass能力维度体系涵盖通用能力和特色能力两大部分。

### 通用能力

通用能力涵盖学科综合能力、知识能力、语言能力、理解能力、推理能力、安全能力，共计六大维度构造立体全面的模型能力评价体系。

#### 学科综合能力

该维度旨在从人类成长角度，借鉴教育学中的分类逻辑，从学科综合能力层面为模型能力评测提供维度支撑。本维度的核心思路是从义务教育、高等教育以及职业教育等角度，通过对各级学科进行分类，构建完整的学科能力评测方案。

#### 知识能力

知识能力具体衡量模型对于各类知识的掌握情况，包括但不限于社会常识、专业领域知识等。该能力项希望模型能准确、完善的回答各类知识性问题。

#### 推理能力

推理能力是通用人工智能的重要能力维度，该维度旨在系统性评估模型的推理能力，包括但不限于数学计算能力，逻辑推理能力，因果推断能力，代码生成与修改能力等。

#### 理解能力

理解能力旨在评估模型对文字的理解能力，包括不限于：

- 修辞手法理解与分析：理解文字中使用的各类修辞手法，能对相关修辞手法进行分析与解释。
- 文字内容总结：针对给定内容进行内容总结和信息抽取。
- 文字内容创作：围绕给定的主题或要求进行开放式或半开放式的内容创作。

#### 语言能力

语言能力旨在评估模型在语言先验上的表现，该维度能力包括但不限于：

- 字词理解与生成：从字词层面理解语言，并能完成诸如字词识别与分类，字词含义解释，字词生成等任务。
- 语法理解与修改：理解文字中的语法，并能错误语法表达进行识别和修改。
- 文字跨语言翻译：针对给定的源语言，翻译到目标语言。在多语种能力维度评估现有大模型的能力。

#### 安全能力

OpenCompass结合大语言模型的技术特点，对模型输出是否合法合规、安全无害设计相应维度进行评测，助力安全、负责任的大模型研发。改维度能力包括但不限于：

- 公平性
- 合法性
- 无害性
- 伦理道德
- 保护隐私

## 评测方法

OpenCompass采取客观评测与主观评测相结合的方法。针对具有确定性答案的能力维度和场景，通过构造丰富完善的评测集，对模型能力进行综合评价。针对体现模型能力的开放式或半开放式的问题、模型安全问题等，采用主客观相结合的评测方式。

### 客观评测

针对具有标准答案的客观问题，我们可以我们可以通过使用定量指标比较模型的输出与标准答案的差异，并根据结果衡量模型的性能。同时，由于大语言模型输出自由度较高，在评测阶段，我们需要对其输入和输出作一定的规范和设计，尽可能减少噪声输出在评测阶段的影响，才能对模型的能力有更加完整和客观的评价。

为了更好地激发出模型在题目测试领域的能力，并引导模型按照一定的模板输出答案，OpenCompass采用提示词工程 （prompt engineering）和语境学习（in-context learning）进行客观评测。

在客观评测的具体实践中，我们通常采用下列两种方式进行模型输出结果的评测：

- **判别式评测**：该评测方式基于将问题与候选答案组合在一起，计算模型在所有组合上的困惑度（perplexity），并选择困惑度最小的答案作为模型的最终输出。例如，若模型在 `问题? 答案1` 上的困惑度为 0.1，在 `问题? 答案2` 上的困惑度为 0.2，最终我们会选择 `答案1` 作为模型的输出。

- **生成式评测**：该评测方式主要用于生成类任务，如语言翻译、程序生成、逻辑分析题等。具体实践时，使用问题作为模型的原始输入，并留白答案区域待模型进行后续补全。我们通常还需要对其输出进行后处理，以保证输出满足数据集的要求。

### 主观评测(即将发布)

语言表达生动精彩，变化丰富，大量的场景和能力无法凭借客观指标进行评测。针对如模型安全和模型语言能力的评测，以人的主观感受为主的评测更能体现模型的真实能力，并更符合大模型的实际使用场景。

OpenCompass采取的主观评测方案是指借助受试者的主观判断对具有对话能力的大语言模型进行能力评测。在具体实践中，我们提前基于模型的能力维度构建主观测试问题集合，并将不同模型对于同一问题的不同回复展现给受试者，收集受试者基于主观感受的评分。由于主观测试成本高昂，本方案同时也采用使用性能优异的大语言模拟人类进行主观打分。在实际评测中，本文将采用真实人类专家的主观评测与基于模型打分的主观评测相结合的方式开展模型能力评估。

在具体开展主观评测时，OpenComapss采用**单模型回复满意度统计**和**多模型满意度比较**两种方式开展具体的评测工作。
