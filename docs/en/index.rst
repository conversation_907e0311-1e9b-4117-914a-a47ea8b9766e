Welcome to OpenCompass' documentation!
==========================================

Getting started with OpenCompass
-------------------------------

To help you quickly familiarized with OpenCompass, we recommend you to walk through the following documents in order:

- First read the GetStarted_ section set up the environment, and run a mini experiment.

- Then learn its basic usage through the UserGuides_.

- If you want to tune the prompts, refer to the Prompt_.

- If you want to customize some modules, like adding a new dataset or model, we have provided the AdvancedGuides_.

- There are more handy tools, such as prompt viewer and lark bot reporter, all presented in Tools_.

We always welcome *PRs* and *Issues* for the betterment of OpenCompass.

.. _GetStarted:
.. toctree::
   :maxdepth: 1
   :caption: Get Started

   get_started/installation.md
   get_started/quick_start.md
   get_started/faq.md

.. _UserGuides:
.. toctree::
   :maxdepth: 1
   :caption: User Guides

   user_guides/framework_overview.md
   user_guides/config.md
   user_guides/datasets.md
   user_guides/models.md
   user_guides/evaluation.md
   user_guides/experimentation.md
   user_guides/metrics.md
   user_guides/deepseek_r1.md
   user_guides/interns1.md

.. _Prompt:
.. toctree::
   :maxdepth: 1
   :caption: Prompt

   prompt/overview.md
   prompt/prompt_template.md
   prompt/meta_template.md
   prompt/chain_of_thought.md


.. _AdvancedGuides:
.. toctree::
   :maxdepth: 1
   :caption: Advanced Guides

   advanced_guides/new_dataset.md
   advanced_guides/custom_dataset.md
   advanced_guides/new_model.md
   advanced_guides/evaluation_lmdeploy.md
   advanced_guides/accelerator_intro.md
   advanced_guides/math_verify.md
   advanced_guides/llm_judge.md
   advanced_guides/code_eval.md
   advanced_guides/code_eval_service.md
   advanced_guides/subjective_evaluation.md
   advanced_guides/persistence.md

.. _Tools:
.. toctree::
   :maxdepth: 1
   :caption: Tools

   tools.md

.. _Dataset List:
.. toctree::
   :maxdepth: 1
   :caption: Dataset List

   dataset_statistics.md

.. _Notes:
.. toctree::
   :maxdepth: 1
   :caption: Notes

   notes/contribution_guide.md
   notes/academic.md

Indexes & Tables
==================

* :ref:`genindex`
* :ref:`search`
